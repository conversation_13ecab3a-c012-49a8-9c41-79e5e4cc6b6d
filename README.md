# React Portfolio Website

A modern, responsive portfolio website built with React.js, showcasing skills, projects, and professional experience. Features smooth animations, mobile-first design, and clean, maintainable code.

## 🚀 Features

- **Responsive Design**: Optimized for all devices (mobile, tablet, desktop)
- **Modern UI/UX**: Clean design with smooth animations and transitions
- **Interactive Navigation**: Smooth scrolling navigation with active states
- **Project Showcase**: Filterable project gallery with live demos and source code links
- **Skills Visualization**: Animated skill bars and technology icons
- **Contact Form**: Functional contact form with validation
- **SEO Optimized**: Meta tags and semantic HTML for better search engine visibility
- **Performance Optimized**: Fast loading with optimized assets

## 🛠️ Built With

- **React.js** - Frontend framework
- **Vite** - Build tool and development server
- **Lucide React** - Beautiful icons
- **CSS3** - Styling with modern features (Grid, Flexbox, Animations)
- **Google Fonts** - Typography (Inter font family)

## 📦 Installation

1. Clone the repository:
```bash
git clone <your-repo-url>
cd react-portfolio
```

2. Install dependencies:
```bash
npm install
```

3. Start the development server:
```bash
npm run dev
```

4. Open [http://localhost:5173](http://localhost:5173) in your browser

## 🎨 Customization

### Personal Information
Update the following files with your personal information:

1. **src/components/Hero.jsx** - Name, title, description, and social links
2. **src/components/About.jsx** - About section content and stats
3. **src/components/Resume.jsx** - Work experience and education
4. **src/components/Contact.jsx** - Contact information
5. **index.html** - Meta tags, title, and SEO information



## 🚀 Deployment

### Build for Production
```bash
npm run build
```

### Deploy to Netlify
1. Build the project: `npm run build`
2. Drag and drop the `dist` folder to [Netlify](https://netlify.com)

### Deploy to Vercel
1. Install Vercel CLI: `npm i -g vercel`
2. Run: `vercel`
3. Follow the prompts



## 📱 Responsive Breakpoints

- **Mobile**: < 768px
- **Tablet**: 768px - 1024px
- **Desktop**: > 1024px

## 🎯 Performance Tips

- Optimize images before adding them
- Use WebP format for better compression
- Minimize the number of external dependencies
- Enable gzip compression on your server

## 📄 License

This project is open source and available under the [MIT License](LICENSE).

## 🤝 Contributing

1. Fork the project
2. Create your feature branch (`git checkout -b feature/AmazingFeature`)
3. Commit your changes (`git commit -m 'Add some AmazingFeature'`)
4. Push to the branch (`git push origin feature/AmazingFeature`)
5. Open a Pull Request

## 📞 Support

If you have any questions or need help customizing the portfolio, feel free to reach out!

---

**Happy coding!** 🚀
