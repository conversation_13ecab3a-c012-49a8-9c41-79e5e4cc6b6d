import React from 'react'
import { Download, Calendar, MapPin, Briefcase, GraduationCap } from 'lucide-react'
import './Resume.css'

const Resume = () => {
  const experiences = [
    
    {
      title: "Freelance Developer",
      company: "Freelance",
      location: "Remote",
      period: "2021 - Present",
      description: [
        "Developed and maintained web applications for clients",
        "Collaborated with cross-functional teams to deliver high-quality software",
        "Implemented responsive design and ensured cross-browser compatibility"
      ]
    }
  ]

  const education = [
    {
      degree: 'SSLC',
      school: 'St Columbas School',
      location: 'New Delhi',
      period: '2011 - 2022',
      details: 'Scored 72.8%'
    },
    {
      degree: 'Pre-Degree in Computer Science',
      school: 'St Marys Central School',
      location: 'Idukki, Kerala',
      period: '2022-2024',
      details: 'Scored 88%'
    }
  ]

  const handleDownloadResume = () => {
    window.open("https://dariogeorge.vercel.app", "_blank", "noopener,noreferrer");
  }

  return (
    <section id="resume" className="resume">
      <div className="resume-container">
        <h2 className="section-title">Resume</h2>
        <p className="section-subtitle">
          My professional journey and educational background
        </p>

        <button className="download-btn" onClick={handleDownloadResume}>
          <Download size={20} />
          Load Resume
        </button>

        <div className="resume-content">
          <div className="resume-section">
            <h3 className="resume-section-title">
              <Briefcase size={24} />
              Work Experience
            </h3>
            <div className="timeline">
              {experiences.map((exp, index) => (
                <div key={index} className="timeline-item">
                  <div className="timeline-marker"></div>
                  <div className="timeline-content">
                    <h4 className="job-title">{exp.title}</h4>
                    <div className="job-meta">
                      <span className="company">{exp.company}</span>
                      <span className="location">
                        <MapPin size={16} />
                        {exp.location}
                      </span>
                      <span className="period">
                        <Calendar size={16} />
                        {exp.period}
                      </span>
                    </div>
                    <ul className="job-description">
                      {exp.description.map((item, idx) => (
                        <li key={idx}>{item}</li>
                      ))}
                    </ul>
                  </div>
                </div>
              ))}
            </div>
          </div>

          <div className="resume-section">
            <h3 className="resume-section-title">
              <GraduationCap size={24} />
              Education
            </h3>
            <div className="timeline">
              {education.map((edu, index) => (
                <div key={index} className="timeline-item">
                  <div className="timeline-marker"></div>
                  <div className="timeline-content">
                    <h4 className="degree-title">{edu.degree}</h4>
                    <div className="edu-meta">
                      <span className="school">{edu.school}</span>
                      <span className="location">
                        <MapPin size={16} />
                        {edu.location}
                      </span>
                      <span className="period">
                        <Calendar size={16} />
                        {edu.period}
                      </span>
                    </div>
                    <p className="edu-details">{edu.details}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}

export default Resume
