import React from 'react'
import { Ch<PERSON>ronDown, Github, Linkedin, Mail } from 'lucide-react'
import './Hero.css'

const Hero = () => {
  const scrollToSection = (sectionId) => {
    const element = document.getElementById(sectionId)
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' })
    }
  }

  return (
    <section id="hero" className="hero">
      <div className="hero-container">
        <div className="hero-content">
          <h1 className="hero-title">
            Hi, I'm <span className="highlight">Dario</span>
          </h1>
          <h2 className="hero-subtitle">
            Full Stack Developer & UI/UX Designer
          </h2>
          <p className="hero-description">
            I create beautiful, responsive web applications with modern technologies.
            Passionate about clean code, user experience, and innovative solutions.
          </p>
          
          <div className="hero-buttons">
            <button 
              className="btn btn-primary"
              onClick={() => scrollToSection('projects')}
            >
              View My Work
            </button>
            <button 
              className="btn btn-secondary"
              onClick={() => scrollToSection('contact')}
            >
              Get In Touch
            </button>
          </div>

          <div className="social-links">
            <a 
              href="https://github.com/dariogeorge21" 
              target="_blank" 
              rel="noopener noreferrer"
              className="social-link"
              aria-label="GitHub"
            >
              <Github size={24} />
            </a>
            <a 
              href="https://linkedin.com/in/dariogeorge21" 
              target="_blank" 
              rel="noopener noreferrer"
              className="social-link"
              aria-label="LinkedIn"
            >
              <Linkedin size={24} />
            </a>
            <a 
              href="mailto:<EMAIL>"
              target="_blank" 
              rel="noopener noreferrer"
              className="social-link"
              aria-label="Email"
            >
              <Mail size={24} />
            </a>
          </div>
        </div>

        <div className="hero-image">
          <div className="image-placeholder">
            <div className="avatar">
              <span>DG</span>
            </div>
          </div>
        </div>
      </div>

      <button 
        className="scroll-indicator"
        onClick={() => scrollToSection('about')}
        aria-label="Scroll to about section"
      >
        <ChevronDown size={24} />
      </button>
    </section>
  )
}

export default Hero
