import React from 'react'
import { Code, Palette, Zap, Users } from 'lucide-react'
import './About.css'

const About = () => {
  const features = [
    {
      icon: <Code size={32} />,
      title: 'Clean Code',
      description: 'Writing maintainable, scalable, and efficient code following best practices.'
    },
    {
      icon: <Palette size={32} />,
      title: 'UI/UX Design',
      description: 'Creating beautiful and intuitive user interfaces with great user experience.'
    },
    {
      icon: <Zap size={32} />,
      title: 'Performance',
      description: 'Optimizing applications for speed, efficiency, and excellent performance.'
    },
    {
      icon: <Users size={32} />,
      title: 'Collaboration',
      description: 'Working effectively in teams and communicating technical concepts clearly.'
    }
  ]

  return (
    <section id="about" className="about">
      <div className="about-container">
        <div className="about-content">
          <div className="about-text">
            <h2 className="section-title">About Me</h2>
            <p className="about-description">
              I'm a passionate full-stack developer with a love for creating digital experiences 
              that make a difference. With expertise in modern web technologies, I enjoy turning 
              complex problems into simple, beautiful, and intuitive solutions.
            </p>
            <p className="about-description">
              When I'm not coding, you can find me exploring new technologies, contributing to 
              open-source projects, or sharing knowledge with the developer community. I believe 
              in continuous learning and staying up-to-date with the latest industry trends.
            </p>
            
            <div className="about-stats">
              <div className="stat">
                <h3>5+</h3>
                <p>Projects Completed</p>
              </div>
              <div className="stat">
                <h3>3+</h3>
                <p>Years Experience</p>
              </div>
              <div className="stat">
                <h3>90%</h3>
                <p>Client Satisfaction</p>
              </div>
            </div>
          </div>

          <div className="about-image">
            <div className="image-container">
              <div className="profile-image">
                <span>👨‍💻</span>
              </div>
            </div>
          </div>
        </div>

        <div className="features-grid">
          {features.map((feature, index) => (
            <div key={index} className="feature-card">
              <div className="feature-icon">
                {feature.icon}
              </div>
              <h3 className="feature-title">{feature.title}</h3>
              <p className="feature-description">{feature.description}</p>
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}

export default About
