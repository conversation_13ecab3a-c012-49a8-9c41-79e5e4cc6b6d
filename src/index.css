/* Import Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

:root {
  /* Color Variables */
  --primary-color: #2563eb;
  --primary-light: #3b82f6;
  --secondary-color: #667eea;
  --accent-color: #ffd700;
  --text-dark: #1f2937;
  --text-gray: #6b7280;
  --text-light: #9ca3af;
  --bg-white: #ffffff;
  --bg-gray: #f8fafc;
  --bg-dark: #1f2937;

  /* Typography */
  --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;

  /* Spacing */
  --section-padding: 6rem 2rem;
  --container-max-width: 1200px;

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);

  /* Border Radius */
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;

  /* Transitions */
  --transition-fast: 0.15s ease;
  --transition-normal: 0.3s ease;
  --transition-slow: 0.5s ease;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
  font-size: 16px;
}

body {
  font-family: var(--font-family);
  line-height: 1.6;
  color: var(--text-dark);
  background-color: var(--bg-white);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  overflow-x: hidden;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  font-weight: 600;
  line-height: 1.2;
  margin-bottom: 0.5em;
}

p {
  margin-bottom: 1em;
}

/* Links */
a {
  color: inherit;
  text-decoration: none;
  transition: var(--transition-normal);
}

/* Buttons */
button {
  font-family: inherit;
  cursor: pointer;
  border: none;
  transition: var(--transition-normal);
}

/* Images */
img {
  max-width: 100%;
  height: auto;
}

/* Lists */
ul, ol {
  list-style: none;
}

/* Form Elements */
input, textarea, select {
  font-family: inherit;
  font-size: inherit;
}

/* Focus Styles */
*:focus {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

/* Selection Styles */
::selection {
  background-color: var(--primary-color);
  color: white;
}
